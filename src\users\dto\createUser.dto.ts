import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empty, <PERSON><PERSON><PERSON>, <PERSON>,  } from 'class-validator';
export class CreateUserDto{
    @IsNotEmpty()
    @IsString()
    firstName:string;

    @IsString()
    @IsNotEmpty()
    lastName:string;

    @IsNotEmpty()
    @IsString()
    @IsEmail()
    email:string;

    @IsNotEmpty()
    @IsString()
    password:string;

    @IsNotEmpty()
    @IsString()
    mobileNumber:string;

    @IsNotEmpty()
    @IsString()
    gender:string;

    @IsNotEmpty()
    @IsString()
    dob:string;
}